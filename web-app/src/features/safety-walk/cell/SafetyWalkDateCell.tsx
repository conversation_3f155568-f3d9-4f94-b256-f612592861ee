import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { SafetyWalkRead } from '../safetyWalkTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type SafetyWalkDateCellParam = GridRenderCellParams<SafetyWalkRead, Date | undefined, string>;

export default function SafetyWalkDateCell(params: SafetyWalkDateCellParam) {
  const { formattedValue: formattedDate } = params;

  if (!formattedDate) {
    return null;
  }

  return (
    <Cell title={formattedDate}>
      <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{formattedDate}</CellText>
    </Cell>
  );
}
