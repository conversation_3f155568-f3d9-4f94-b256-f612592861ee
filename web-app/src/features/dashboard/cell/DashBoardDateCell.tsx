import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';
import { DashboardSettingRead } from '../dashBoardTypes';

type DashboardDateCellParam = GridRenderCellParams<DashboardSettingRead, Date | undefined, string>;

export default function DashboardDateCell(params: DashboardDateCellParam) {
  const { formattedValue: formattedDate } = params;

  if (!formattedDate) {
    return null;
  }

  return (
    <Cell title={formattedDate}>
      <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{formattedDate}</CellText>
    </Cell>
  );
}
