import { Box } from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';
import { InstructionRead } from '../instructionTypes';

function InstructionDateCell({ row: instruction }: GridRenderCellParams<InstructionRead, number, string>) {
  const getDatePlusTimeString = (date: Date) => {
    const datePlusDay = date.toDateString();
    const hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${datePlusDay} ${hours}:${minutes}`;
  };

  return (
    <Box display="flex">
      <Box width="195px">
        <DateRangeIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
        {getDatePlusTimeString(new Date(instruction.startTime))}{' '}
      </Box>
      <Box width="20px" mr="5px">
        <ArrowRightAltIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
      </Box>
      <Box width="180px">
        {instruction.endTime !== null ? getDatePlusTimeString(new Date(instruction.endTime)) : 'until closed'}
      </Box>
    </Box>
  );
}

export default InstructionDateCell;
