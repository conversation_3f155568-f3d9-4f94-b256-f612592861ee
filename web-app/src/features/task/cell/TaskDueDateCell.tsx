import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import EventRepeatIcon from '@mui/icons-material/EventRepeat';
import dayjs from 'dayjs';
import { TaskRead, TaskStatus } from '../taskTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';
import { MUIColor } from '../../../theme';

type DueDateCellParam = GridRenderCellParams<TaskRead, Date | undefined, string>;

const getColor = (currentTask: TaskRead): MUIColor => {
  if (currentTask.status !== TaskStatus.TODO) {
    return 'inherit';
  }
  if (dayjs(currentTask.dueDate).isBefore(dayjs())) {
    return 'error.main';
  }
  if (dayjs(currentTask.dueDate).isBefore(dayjs().endOf('day').endOf('minute'))) {
    return 'warning.light';
  }
  return 'inherit';
};

export default function TaskDueDateCell(params: DueDateCellParam) {
  const { row: task, formattedValue: formattedDueDate } = params;

  if (!formattedDueDate) {
    return null;
  }

  return (
    <Cell color={getColor(task)} title={formattedDueDate}>
      {!task.recurrence ? (
        <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
      ) : (
        <EventRepeatIcon fontSize="small" sx={{ mr: 0.5 }} />
      )}
      <CellText>{formattedDueDate}</CellText>
    </Cell>
  );
}
