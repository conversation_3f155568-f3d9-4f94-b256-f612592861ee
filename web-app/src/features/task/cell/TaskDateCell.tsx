import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { TaskRead } from '../taskTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type DateCellParam = GridRenderCellParams<TaskRead, Date | undefined, string>;

export default function DateCell(params: DateCellParam) {
  const { formattedValue: formattedDate } = params;

  if (!formattedDate) {
    return null;
  }

  return (
    <Cell title={formattedDate}>
      <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{formattedDate}</CellText>
    </Cell>
  );
}
