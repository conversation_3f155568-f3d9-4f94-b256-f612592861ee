import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { LototoItemRead } from '../lototoItemTypes';
import CellText from '../../../../components/CellText';
import Cell from '../../../../components/Cell';

export default function LototoItemPlannedDateCell({ row }: GridRenderCellParams<LototoItemRead>) {
  const { lototo } = row;

  if (!lototo || !lototo.plannedDate) {
    return null;
  }

  // Using toDateString to leave time out of date string
  const dateString = new Date(lototo.plannedDate).toDateString();

  return (
    <Cell title={dateString}>
      <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{dateString}</CellText>
    </Cell>
  );
}
