import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { ShiftLogRead } from '../shiftLogTypes';
import { getDatePlusTimeString } from '../../../../utils';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

export default function ShiftLogDateCell(params: GridRenderCellParams<ShiftLogRead>) {
  const { row } = params;
  const {date} = row;

  if (!date) {
    return null;
  }

  const dateString = getDatePlusTimeString(new Date(date));

  return (
    <Cell title={dateString}>
      <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{dateString}</CellText>
    </Cell>
  );
}
