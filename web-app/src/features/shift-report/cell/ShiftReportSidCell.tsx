import { GridRenderCellParams } from '@mui/x-data-grid';
import { ShiftReportRead } from '../shiftReportTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

export default function ShiftReportSidCell(params: GridRenderCellParams<ShiftReportRead>) {
  const { row } = params;
  const {sid} = row;

  return (
    <Cell title={sid?.toString()}>
      <CellText>{sid}</CellText>
    </Cell>
  );
}
