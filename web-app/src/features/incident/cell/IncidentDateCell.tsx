import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { IncidentListRead } from '../incidentTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type IncidentDateCellParam = GridRenderCellParams<IncidentListRead, Date | undefined, string>;

export default function IncidentDateCell(params: IncidentDateCellParam) {
  const { formattedValue: formattedDate } = params;

  if (!formattedDate) {
    return null;
  }

  return (
    <Cell title={formattedDate}>
      <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{formattedDate}</CellText>
    </Cell>
  );
}
