import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { ObservationScoreRead } from '../observationTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type ObservationDateCellParam = GridRenderCellParams<ObservationScoreRead, Date | undefined, string>;

export default function ObservationDateCell(params: ObservationDateCellParam) {
  const { formattedValue: formattedDate } = params;

  if (!formattedDate) {
    return null;
  }

  return (
    <Cell title={formattedDate}>
      <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{formattedDate}</CellText>
    </Cell>
  );
}
